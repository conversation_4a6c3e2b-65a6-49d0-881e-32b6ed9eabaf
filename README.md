# Python 小工具集

一个实用的 Python 工具集合，目前包含写真批量下载器。

## 功能特性

### 写真批量下载器

一个功能强大的写真图片批量下载工具，支持从 [mxd009.cc](https://www.mxd009.cc) 网站批量下载写真图片。

**主要功能：**
- 🔍 关键词搜索图片集
- 📋 支持直接粘贴网址下载
- 🖼️ 缩略图预览功能
- 📊 实时下载进度显示
- 📁 自动创建分类文件夹
- ⚡ 多线程并发下载
- 🛑 支持取消下载任务
- 🎯 单个或批量下载选择

## 系统要求

- Python 3.12+
- Windows/Linux/macOS

## 安装说明

### 使用 uv（推荐）

```bash
# 克隆项目
git clone <repository-url>
cd python-tools

# 安装依赖
uv sync
```

### 使用 pip

```bash
# 克隆项目
git clone <repository-url>
cd python-tools

# 安装依赖
pip install -r requirements.txt
```

## 使用方法

### 启动写真下载器

```bash
# 使用 uv
uv run python src/python_tools/photo_downloader.py

# 或直接运行
python src/python_tools/photo_downloader.py
```

### 操作指南

1. **搜索图片集**
   - 在搜索框输入关键词，点击"搜索"
   - 或直接粘贴 mxd009.cc 的网址

2. **预览图片**
   - 点击列表中的任意项目查看缩略图
   - 点击"显示更多缩略图"查看完整图片集预览
   - 点击"显示缩略图"查看所有搜索结果的缩略图

3. **下载图片**
   - 选择单个项目后点击"下载当前选中"
   - 或点击"下载全部"批量下载所有搜索结果
   - 支持随时取消下载任务

4. **文件组织**
   - 所有下载的图片会保存到 `data/photo_downloader/` 目录下
   - 图片会自动保存到以作者名命名的文件夹中
   - 每个图片集会创建独立的子文件夹

## 下载路径说明

下载的图片会按以下结构保存：

```
data/photo_downloader/
├── [作者名1]/
│   ├── [图片集标题1]/
│   │   ├── 001.jpg
│   │   ├── 002.jpg
│   │   └── ...
│   └── [图片集标题2]/
│       ├── 001.jpg
│       └── ...
└── [作者名2]/
    └── [图片集标题3]/
        ├── 001.jpg
        └── ...
```

**示例：**
- 作者"小美女"的图片集"夏日写真"会保存在：`data/photo_downloader/小美女/夏日写真/`
- 如果没有作者信息，则直接以图片集标题创建文件夹

## 技术架构

- **界面框架**: PySide6 (Qt6)
- **网络请求**: requests + BeautifulSoup4
- **多线程**: QThread
- **图片处理**: QPixmap

## 项目结构

```
python-tools/
├── data/                          # 数据存储目录
│   └── photo_downloader/          # 写真下载器数据目录
│       └── [作者名]/
│           └── [图片集标题]/
├── src/
│   └── python_tools/
│       └── photo_downloader.py    # 写真下载器主程序
├── main.py                        # 项目入口
├── pyproject.toml                 # 项目配置
└── README.md                      # 说明文档
```
