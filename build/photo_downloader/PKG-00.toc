('E:\\Python\\python-tools\\build\\photo_downloader\\photo_downloader.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'E:\\Python\\python-tools\\build\\photo_downloader\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'E:\\Python\\python-tools\\build\\photo_downloader\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'E:\\Python\\python-tools\\build\\photo_downloader\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'E:\\Python\\python-tools\\build\\photo_downloader\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'E:\\Python\\python-tools\\build\\photo_downloader\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'E:\\Python\\python-tools\\build\\photo_downloader\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pyside6',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyside6.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('photo_downloader',
   'E:\\Python\\python-tools\\src\\python_tools\\photo_downloader.py',
   'PYSOURCE'),
  ('python312.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python312.dll',
   'BINARY'),
  ('PySide6\\plugins\\tls\\qschannelbackend.dll',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\plugins\\tls\\qschannelbackend.dll',
   'BINARY'),
  ('PySide6\\plugins\\networkinformation\\qnetworklistmanager.dll',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\plugins\\networkinformation\\qnetworklistmanager.dll',
   'BINARY'),
  ('PySide6\\plugins\\tls\\qopensslbackend.dll',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\plugins\\tls\\qopensslbackend.dll',
   'BINARY'),
  ('PySide6\\plugins\\tls\\qcertonlybackend.dll',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\plugins\\tls\\qcertonlybackend.dll',
   'BINARY'),
  ('PySide6\\opengl32sw.dll',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\opengl32sw.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qicns.dll',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qgif.dll',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qtiff.dll',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforms\\qoffscreen.dll',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qwebp.dll',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qjpeg.dll',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qico.dll',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qtga.dll',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qpdf.dll',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qpdf.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qwbmp.dll',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforminputcontexts\\qtvirtualkeyboardplugin.dll',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\plugins\\platforminputcontexts\\qtvirtualkeyboardplugin.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qsvg.dll',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforms\\qdirect2d.dll',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\plugins\\platforms\\qdirect2d.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforms\\qminimal.dll',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforms\\qwindows.dll',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PySide6\\plugins\\generic\\qtuiotouchplugin.dll',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PySide6\\plugins\\iconengines\\qsvgicon.dll',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PySide6\\plugins\\styles\\qmodernwindowsstyle.dll',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\plugins\\styles\\qmodernwindowsstyle.dll',
   'BINARY'),
  ('select.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('PySide6\\QtNetwork.pyd',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\QtNetwork.pyd',
   'EXTENSION'),
  ('shiboken6\\Shiboken.pyd',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\shiboken6\\Shiboken.pyd',
   'EXTENSION'),
  ('PySide6\\QtGui.pyd',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\QtGui.pyd',
   'EXTENSION'),
  ('PySide6\\QtWidgets.pyd',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\QtWidgets.pyd',
   'EXTENSION'),
  ('PySide6\\QtCore.pyd',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\QtCore.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp312-win_amd64.pyd',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp312-win_amd64.pyd',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\charset_normalizer\\md.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\VCRUNTIME140.dll',
   'BINARY'),
  ('PySide6\\Qt6Network.dll',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\Qt6Network.dll',
   'BINARY'),
  ('shiboken6\\MSVCP140.dll',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\shiboken6\\MSVCP140.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PySide6\\Qt6Core.dll',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\Qt6Core.dll',
   'BINARY'),
  ('PySide6\\Qt6Gui.dll',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\Qt6Gui.dll',
   'BINARY'),
  ('PySide6\\Qt6Pdf.dll',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\Qt6Pdf.dll',
   'BINARY'),
  ('PySide6\\Qt6VirtualKeyboard.dll',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\Qt6VirtualKeyboard.dll',
   'BINARY'),
  ('PySide6\\Qt6Svg.dll',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\Qt6Svg.dll',
   'BINARY'),
  ('PySide6\\Qt6Widgets.dll',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\Qt6Widgets.dll',
   'BINARY'),
  ('libssl-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('PySide6\\VCRUNTIME140.dll',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\VCRUNTIME140.dll',
   'BINARY'),
  ('PySide6\\pyside6.abi3.dll',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\pyside6.abi3.dll',
   'BINARY'),
  ('python3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python3.dll',
   'BINARY'),
  ('shiboken6\\shiboken6.abi3.dll',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\shiboken6\\shiboken6.abi3.dll',
   'BINARY'),
  ('PySide6\\VCRUNTIME140_1.dll',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PySide6\\MSVCP140.dll',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\MSVCP140.dll',
   'BINARY'),
  ('shiboken6\\VCRUNTIME140.dll',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\shiboken6\\VCRUNTIME140.dll',
   'BINARY'),
  ('shiboken6\\VCRUNTIME140_1.dll',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\shiboken6\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PySide6\\MSVCP140_2.dll',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\MSVCP140_2.dll',
   'BINARY'),
  ('PySide6\\MSVCP140_1.dll',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\MSVCP140_1.dll',
   'BINARY'),
  ('PySide6\\Qt6Quick.dll',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\Qt6Quick.dll',
   'BINARY'),
  ('PySide6\\Qt6Qml.dll',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\Qt6Qml.dll',
   'BINARY'),
  ('PySide6\\Qt6OpenGL.dll',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\Qt6OpenGL.dll',
   'BINARY'),
  ('PySide6\\Qt6QmlModels.dll',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\Qt6QmlModels.dll',
   'BINARY'),
  ('PySide6\\Qt6QmlMeta.dll',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\Qt6QmlMeta.dll',
   'BINARY'),
  ('PySide6\\Qt6QmlWorkerScript.dll',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\Qt6QmlWorkerScript.dll',
   'BINARY'),
  ('PySide6\\translations\\qt_cs.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_cs.qm',
   'DATA'),
  ('PySide6\\translations\\qt_de.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_de.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_he.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_he.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ar.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_nn.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_nn.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_en.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_en.qm',
   'DATA'),
  ('PySide6\\translations\\qt_sl.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_sl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_tr.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_lv.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_lv.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_es.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_es.qm',
   'DATA'),
  ('PySide6\\translations\\qt_nl.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_nl.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_it.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_it.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ka.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_ka.qm',
   'DATA'),
  ('PySide6\\translations\\qt_zh_CN.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PySide6\\translations\\qt_es.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_es.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ca.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_nl.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_nl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_gd.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_gd.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ko.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_bg.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PySide6\\translations\\qt_sk.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_sk.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_zh_TW.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ja.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PySide6\\translations\\qt_lt.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_lt.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ar.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_ar.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ar.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_da.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_da.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ka.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_ka.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_zh_CN.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_hr.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_hr.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_tr.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_sk.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_sk.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_fi.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_cs.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PySide6\\translations\\qt_bg.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_bg.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_zh_CN.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_zh_CN.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_gd.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ru.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_ru.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_fr.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ka.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_ka.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_hr.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_hr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_pt_BR.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_pt_BR.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_da.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_da.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_pl.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_hu.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PySide6\\translations\\qt_zh_TW.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_bg.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_it.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_it.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_uk.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PySide6\\translations\\qt_en.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_en.qm',
   'DATA'),
  ('PySide6\\translations\\qt_fi.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_fi.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_pl.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_pt_PT.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_pt_PT.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ja.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PySide6\\translations\\qt_sv.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_sv.qm',
   'DATA'),
  ('PySide6\\translations\\qt_hu.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_hu.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_gl.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_fr.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_fr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_tr.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_tr.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_fr.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_gl.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_gl.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_fa.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_fa.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ko.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_ko.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ca.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_ca.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_zh_TW.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PySide6\\translations\\qt_nn.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_nn.qm',
   'DATA'),
  ('PySide6\\translations\\qt_hr.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_hr.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_pt_BR.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_pt_BR.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_de.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_de.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_sl.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_he.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_he.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ja.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_ja.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_cs.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PySide6\\translations\\qt_fa.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_fa.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_nl.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_nl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_da.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_da.qm',
   'DATA'),
  ('PySide6\\translations\\qt_pl.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_pl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_it.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_it.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_lv.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PySide6\\translations\\qt_pt_BR.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_pt_BR.qm',
   'DATA'),
  ('PySide6\\translations\\qt_uk.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_uk.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ru.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_de.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_de.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ru.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_uk.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_hu.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_en.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_en.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_nn.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_nn.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ko.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ca.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_es.qm',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_es.qm',
   'DATA'),
  ('certifi\\py.typed',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('certifi\\cacert.pem',
   'E:\\Python\\python-tools\\.venv\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('base_library.zip',
   'E:\\Python\\python-tools\\build\\photo_downloader\\base_library.zip',
   'DATA')],
 'python312.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
