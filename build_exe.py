#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Photo Downloader 打包脚本
使用 PyInstaller 将 photo_downloader.py 打包成独立的 exe 文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def main():
    # 获取项目根目录
    project_root = Path(__file__).parent
    source_file = project_root / "src" / "python_tools" / "photo_downloader.py"
    
    if not source_file.exists():
        print(f"错误: 源文件不存在 {source_file}")
        return False
    
    # 创建输出目录
    dist_dir = project_root / "dist"
    build_dir = project_root / "build"
    
    # 清理之前的构建文件
    if dist_dir.exists():
        shutil.rmtree(dist_dir)
    if build_dir.exists():
        shutil.rmtree(build_dir)
    
    # PyInstaller 命令参数
    cmd = [
        "pyinstaller",
        "--onefile",                    # 打包成单个文件
        "--windowed",                   # 不显示控制台窗口
        "--name=photo_downloader",      # 输出文件名
        "--icon=NONE",                  # 不使用图标
        "--clean",                      # 清理临时文件
        "--noconfirm",                  # 不询问覆盖
        "--add-data", f"{project_root}/data;data",  # 添加数据目录（如果存在）
        str(source_file)
    ]
    
    print("开始打包...")
    print(f"源文件: {source_file}")
    print(f"命令: {' '.join(cmd)}")
    
    try:
        # 执行打包命令
        result = subprocess.run(cmd, cwd=project_root, capture_output=True, text=True)
        
        if result.returncode == 0:
            exe_file = dist_dir / "photo_downloader.exe"
            if exe_file.exists():
                file_size = exe_file.stat().st_size / (1024 * 1024)  # MB
                print(f"\n✅ 打包成功!")
                print(f"输出文件: {exe_file}")
                print(f"文件大小: {file_size:.1f} MB")
                print(f"\n该exe文件可以在任意Windows系统上运行，无需安装Python环境。")
                return True
            else:
                print("❌ 打包失败: 未找到输出文件")
                return False
        else:
            print("❌ 打包失败:")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 打包过程中出现异常: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
