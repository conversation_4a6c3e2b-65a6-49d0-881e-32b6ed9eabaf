"""
图片下载器

从txt文件中读取URL并下载图片到指定目录
"""

import os
import re
import time
import logging
import requests
from pathlib import Path
from urllib.parse import urlparse
from typing import List, Dict, Optional, Callable, Set
from PIL import Image
from io import BytesIO
from datetime import datetime


# ==================== 工具函数 ====================

def create_download_directory(directory: str) -> str:
    """创建下载目录"""
    try:
        Path(directory).mkdir(parents=True, exist_ok=True)
        return os.path.abspath(directory)
    except OSError as e:
        logging.error(f"创建目录失败: {e}")
        raise


def get_safe_filename(url: str, max_length: int = 100) -> str:
    """从URL生成安全的文件名"""
    try:
        parsed_url = urlparse(url)
        filename = os.path.basename(parsed_url.path)

        if not filename or '.' not in filename:
            url_parts = parsed_url.path.strip('/').split('/')
            if url_parts and url_parts[-1]:
                filename = url_parts[-1]
            else:
                filename = f"{parsed_url.netloc}_{int(time.time())}.jpg"

        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)

        if '.' not in filename:
            filename += '.jpg'

        if len(filename) > max_length:
            name, ext = os.path.splitext(filename)
            max_name_length = max_length - len(ext)
            filename = name[:max_name_length] + ext

        return filename

    except Exception:
        return f"image_{int(time.time())}.jpg"


def get_unique_filename(directory: str, filename: str) -> str:
    """获取唯一的文件名，如果文件已存在则添加数字后缀"""
    file_path = os.path.join(directory, filename)

    if not os.path.exists(file_path):
        return filename

    name, ext = os.path.splitext(filename)
    counter = 1

    while True:
        new_filename = f"{name}_{counter}{ext}"
        new_file_path = os.path.join(directory, new_filename)

        if not os.path.exists(new_file_path):
            return new_filename

        counter += 1

        if counter > 9999:
            return f"{name}_{int(time.time())}{ext}"


def format_file_size(size_bytes: int) -> str:
    """格式化文件大小显示"""
    if size_bytes < 1024:
        return f"{size_bytes} B"
    elif size_bytes < 1024 * 1024:
        return f"{size_bytes / 1024:.1f} KB"
    elif size_bytes < 1024 * 1024 * 1024:
        return f"{size_bytes / (1024 * 1024):.1f} MB"
    else:
        return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"


def is_valid_image_url(url: str) -> bool:
    """检查URL是否可能是图片URL"""
    try:
        if not url.startswith(('http://', 'https://')):
            return False

        parsed_url = urlparse(url)
        path = parsed_url.path.lower()

        image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg', '.ico']

        for ext in image_extensions:
            if path.endswith(ext):
                return True

        url_lower = url.lower()
        image_keywords = ['image', 'img', 'photo', 'pic', 'picture', 'avatar', 'thumb']

        return any(keyword in url_lower for keyword in image_keywords)

    except Exception:
        return False


# ==================== URL读取器 ====================

class URLReader:
    """从txt文件中读取URL的类"""

    def __init__(self, file_path: str):
        self.file_path = file_path

    def read_urls(self) -> List[str]:
        """从txt文件中读取所有URL"""
        if not os.path.exists(self.file_path):
            raise FileNotFoundError(f"文件不存在: {self.file_path}")

        urls = []

        try:
            with open(self.file_path, 'r', encoding='utf-8') as file:
                for line_num, line in enumerate(file, 1):
                    line = line.strip()

                    if not line or line.startswith('#'):
                        continue

                    if self._is_valid_url(line):
                        urls.append(line)
                        logging.debug(f"读取URL (行 {line_num}): {line}")
                    else:
                        logging.warning(f"无效URL (行 {line_num}): {line}")

        except IOError as e:
            logging.error(f"读取文件失败: {e}")
            raise

        unique_urls = self._remove_duplicates(urls)
        logging.info(f"从 {self.file_path} 读取到 {len(unique_urls)} 个有效URL")

        return unique_urls

    def _is_valid_url(self, url: str) -> bool:
        """验证URL格式"""
        return (
                url.startswith(('http://', 'https://')) and
                len(url) > 10 and
                '.' in url
        )

    def _remove_duplicates(self, urls: List[str]) -> List[str]:
        """去除重复的URL，保持原有顺序"""
        seen: Set[str] = set()
        unique_urls = []

        for url in urls:
            if url not in seen:
                seen.add(url)
                unique_urls.append(url)
            else:
                logging.debug(f"跳过重复URL: {url}")

        return unique_urls


# ==================== 下载结果类 ====================

class DownloadResult:
    """下载结果类"""

    def __init__(self, url: str, success: bool, file_path: Optional[str] = None,
                 error: Optional[str] = None, file_size: int = 0):
        self.url = url
        self.success = success
        self.file_path = file_path
        self.error = error
        self.file_size = file_size


# ==================== 图片下载器 ====================

class ImageDownloader:
    """图片下载器类"""

    def __init__(self, download_dir: str = "downloads",
                 timeout: int = 30,
                 max_retries: int = 3,
                 headers: Optional[Dict[str, str]] = None):
        self.download_dir = create_download_directory(download_dir)
        self.timeout = timeout
        self.max_retries = max_retries
        self.session = requests.Session()

        default_headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        if headers:
            default_headers.update(headers)
        self.session.headers.update(default_headers)

        self.downloaded_count = 0
        self.failed_count = 0
        self.total_size = 0

    def download_single_image(self, url: str,
                              progress_callback: Optional[Callable[[str, int, int], None]] = None) -> DownloadResult:
        """下载单个图片"""
        if not is_valid_image_url(url):
            error_msg = f"URL可能不是图片: {url}"
            logging.warning(error_msg)
            return DownloadResult(url, False, error=error_msg)

        for attempt in range(self.max_retries + 1):
            try:
                logging.info(f"下载图片 (尝试 {attempt + 1}/{self.max_retries + 1}): {url}")

                response = self.session.get(url, timeout=self.timeout, stream=True)
                response.raise_for_status()

                content_type = response.headers.get('content-type', '').lower()
                if not any(img_type in content_type for img_type in ['image', 'jpeg', 'png', 'gif', 'webp']):
                    logging.warning(f"响应内容可能不是图片: {content_type}")

                total_size = int(response.headers.get('content-length', 0))

                filename = get_safe_filename(url)
                filename = get_unique_filename(self.download_dir, filename)
                file_path = os.path.join(self.download_dir, filename)

                downloaded_size = 0
                with open(file_path, 'wb') as file:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            file.write(chunk)
                            downloaded_size += len(chunk)

                            if progress_callback:
                                progress_callback(url, downloaded_size, total_size)

                if self._validate_image(file_path):
                    self.downloaded_count += 1
                    self.total_size += downloaded_size
                    logging.info(f"下载成功: {filename} ({format_file_size(downloaded_size)})")
                    return DownloadResult(url, True, file_path, file_size=downloaded_size)
                else:
                    os.remove(file_path)
                    error_msg = "下载的文件不是有效图片"
                    logging.error(error_msg)
                    return DownloadResult(url, False, error=error_msg)

            except requests.exceptions.RequestException as e:
                error_msg = f"网络请求错误: {e}"
                logging.warning(f"下载失败 (尝试 {attempt + 1}): {error_msg}")

                if attempt == self.max_retries:
                    self.failed_count += 1
                    return DownloadResult(url, False, error=error_msg)
                else:
                    time.sleep(2 ** attempt)

            except Exception as e:
                error_msg = f"未知错误: {e}"
                logging.error(error_msg)
                self.failed_count += 1
                return DownloadResult(url, False, error=error_msg)

        return DownloadResult(url, False, error="下载失败")

    def download_images(self, urls: List[str],
                        progress_callback: Optional[Callable[[int, int, str], None]] = None) -> List[DownloadResult]:
        """批量下载图片"""
        results = []
        total_count = len(urls)

        logging.info(f"开始批量下载 {total_count} 个图片到: {self.download_dir}")

        for i, url in enumerate(urls):
            if progress_callback:
                progress_callback(i + 1, total_count, url)

            result = self.download_single_image(url)
            results.append(result)

            time.sleep(0.5)

        self._print_summary(results)

        return results

    def _validate_image(self, file_path: str) -> bool:
        """验证文件是否为有效图片"""
        try:
            with Image.open(file_path) as img:
                img.verify()
            return True
        except Exception:
            return False

    def _print_summary(self, results: List[DownloadResult]) -> None:
        """打印下载统计信息"""
        total_count = len(results)
        success_count = sum(1 for r in results if r.success)
        failed_count = total_count - success_count

        logging.info("=" * 50)
        logging.info("下载完成统计:")
        logging.info(f"总计: {total_count} 个URL")
        logging.info(f"成功: {success_count} 个")
        logging.info(f"失败: {failed_count} 个")
        logging.info(f"总大小: {format_file_size(self.total_size)}")
        logging.info(f"保存目录: {self.download_dir}")

        if failed_count > 0:
            logging.info("\n失败的URL:")
            for result in results:
                if not result.success:
                    logging.info(f"  {result.url} - {result.error}")

        logging.info("=" * 50)


# ==================== 主函数 ====================

def progress_callback(current: int, total: int, url: str) -> None:
    """进度回调函数"""
    percentage = (current / total) * 100
    print(f"\r进度: {current}/{total} ({percentage:.1f}%) - {url[:50]}...", end='', flush=True)

    if current == total:
        print()


def main(img_txt_path: str, verbose: bool = False) -> bool:
    """
    主函数：从指定的img.txt文件下载图片

    Args:
        img_txt_path: img.txt文件的路径
        verbose: 是否显示详细日志

    Returns:
        是否成功完成（至少下载了一张图片）
    """
    # 设置日志
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler()]
    )

    try:
        logging.info("=" * 60)
        logging.info("图片下载器启动")
        logging.info("=" * 60)

        # 检查img.txt文件是否存在
        if not os.path.exists(img_txt_path):
            logging.error(f"未找到img.txt文件: {img_txt_path}")
            return False

        # 读取URL
        logging.info(f"从文件读取URL: {img_txt_path}")
        url_reader = URLReader(img_txt_path)
        urls = url_reader.read_urls()

        if not urls:
            logging.warning("img.txt文件中没有找到有效的URL")
            return False

        logging.info(f"找到 {len(urls)} 个有效URL")

        # 创建基于时间戳的下载目录
        # 获取当前脚本所在目录
        script_dir = os.path.dirname(os.path.abspath(__file__))
        # 生成时间戳文件夹名称：年月日时分秒
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        download_dir = os.path.join(script_dir, timestamp)

        # 创建下载器
        downloader = ImageDownloader(
            download_dir=download_dir,
            timeout=30,
            max_retries=3
        )

        logging.info(f"下载目录: {downloader.download_dir}")

        # 开始下载
        results = downloader.download_images(urls, progress_callback)

        # 检查结果
        success_count = sum(1 for r in results if r.success)

        logging.info("=" * 60)
        if success_count > 0:
            logging.info(f"✓ 下载完成！成功下载 {success_count} 张图片")
            logging.info(f"图片保存在: {downloader.download_dir}")
        else:
            logging.warning("✗ 没有成功下载任何图片")
        logging.info("=" * 60)

        return success_count > 0

    except Exception as e:
        logging.error(f"下载过程中发生错误: {e}")
        if verbose:
            import traceback
            logging.debug(traceback.format_exc())
        return False


if __name__ == "__main__":
    import sys

    # 获取当前脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    img_txt_path = os.path.join(script_dir, 'img.txt')

    # 检查是否有详细日志参数
    verbose = '-v' in sys.argv or '--verbose' in sys.argv

    print("图片下载器启动")
    print(f"读取文件: {img_txt_path}")
    print("图片将保存到以时间戳命名的文件夹中")
    print("-" * 50)

    success = main(img_txt_path, verbose)

    if success:
        print("\n✓ 下载完成！")
    else:
        print("\n✗ 下载失败！")

    sys.exit(0 if success else 1)
